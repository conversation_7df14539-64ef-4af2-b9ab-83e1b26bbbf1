import { vi } from 'vitest';
import type { P2PConfig, PeerInfo, Message, ConnectionState } from '@p2p/types';

// Mock EventBus
export const mockEventBus = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock P2PConnectionManager
export const mockP2PConnectionManager = {
  createConnection: vi.fn(),
  closeConnection: vi.fn(),
  closeAllConnections: vi.fn(),
  sendData: vi.fn(),
  createDataChannel: vi.fn(),
  getConnectionState: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock SignalingClient
export const mockSignalingClient = {
  connect: vi.fn(),
  disconnect: vi.fn(),
  joinRoom: vi.fn(),
  leaveRoom: vi.fn(),
  sendOffer: vi.fn(),
  sendAnswer: vi.fn(),
  sendIceCandidate: vi.fn(),
  getIsConnected: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock MessageHandler
export const mockMessageHandler = {
  sendMessage: vi.fn(),
  handleReceivedMessage: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock FileTransferManager
export const mockFileTransferManager = {
  initiateTransfer: vi.fn(),
  acceptTransfer: vi.fn(),
  rejectTransfer: vi.fn(),
  cancelTransfer: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock default config
export const mockDefaultP2PConfig: P2PConfig = {
  iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
  signalingUrl: 'http://localhost:3001',
  maxReconnectAttempts: 3,
  reconnectDelay: 1000,
  chunkSize: 16384,
  maxFileSize: 100 * 1024 * 1024,
};

// Mock implementations
vi.mock('@p2p/core', () => ({
  P2PConnectionManager: vi.fn().mockImplementation(() => ({
    ...mockP2PConnectionManager,
    // Ensure all methods return proper values
    createConnection: vi.fn().mockResolvedValue({
      createOffer: vi
        .fn()
        .mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
      createAnswer: vi
        .fn()
        .mockResolvedValue({ type: 'answer', sdp: 'mock-sdp' }),
      setLocalDescription: vi.fn().mockResolvedValue(undefined),
      setRemoteDescription: vi.fn().mockResolvedValue(undefined),
      addIceCandidate: vi.fn().mockResolvedValue(undefined),
    }),
    getConnectionState: vi.fn().mockReturnValue({
      connection: {
        setRemoteDescription: vi.fn().mockResolvedValue(undefined),
        addIceCandidate: vi.fn().mockResolvedValue(undefined),
      },
    }),
  })),
  SignalingClient: vi.fn().mockImplementation(() => ({
    ...mockSignalingClient,
    connect: vi.fn().mockResolvedValue(undefined),
    joinRoom: vi.fn().mockResolvedValue([]),
    getIsConnected: vi.fn().mockReturnValue(true),
  })),
  MessageHandler: vi.fn().mockImplementation(() => ({
    ...mockMessageHandler,
    sendMessage: vi.fn().mockReturnValue({
      id: 'mock-message-id',
      senderId: 'temp-id',
      content: 'mock content',
      timestamp: new Date(),
      type: 'text',
    }),
  })),
  FileTransferManager: vi.fn().mockImplementation(() => ({
    ...mockFileTransferManager,
    initiateTransfer: vi.fn().mockReturnValue({
      id: 'mock-transfer-id',
      fileName: 'mock-file.txt',
      fileSize: 1024,
      senderId: 'user-123',
      receiverId: 'peer-1',
      status: 'pending',
      progress: 0,
      timestamp: new Date(),
    }),
  })),
  EventBus: vi.fn().mockImplementation(() => mockEventBus),
  defaultP2PConfig: mockDefaultP2PConfig,
}));

// Helper functions for tests
export const createMockPeerInfo = (
  overrides?: Partial<PeerInfo>
): PeerInfo => ({
  id: 'mock-peer-id',
  status: 'connected',
  name: 'Mock Peer',
  ...overrides,
});

export const createMockMessage = (overrides?: Partial<Message>): Message => ({
  id: 'mock-message-id',
  senderId: 'mock-sender-id',
  content: 'Mock message content',
  timestamp: new Date(),
  type: 'text',
  ...overrides,
});

export const createMockConnectionState = (
  overrides?: Partial<ConnectionState>
): ConnectionState => ({
  peer: createMockPeerInfo(),
  status: 'connected',
  connection: null,
  dataChannel: null,
  lastActivity: new Date(),
  ...overrides,
});

// Reset all mocks
export const resetP2PMocks = () => {
  Object.values(mockP2PConnectionManager).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });

  Object.values(mockSignalingClient).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });

  Object.values(mockMessageHandler).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });

  Object.values(mockFileTransferManager).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });
};
