// Stub implementations of P2P core classes for testing and development
// These will be replaced with actual implementations when the P2P library is available

import type { PeerInfo, Message, FileTransfer, P2PConfig } from '../types/p2p';

// Event emitter base class
class EventEmitter {
  private handlers = new Map<string, Function[]>();

  on(event: string, handler: Function): void {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, []);
    }
    this.handlers.get(event)!.push(handler);
  }

  off(event: string, handler: Function): void {
    const handlers = this.handlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  emit(event: string, data?: any): void {
    const handlers = this.handlers.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }

  removeAllListeners(): void {
    this.handlers.clear();
  }
}

// P2P Connection Manager stub
export class P2PConnectionManager extends EventEmitter {
  async createConnection(peerId: string): Promise<RTCPeerConnection> {
    // Return a mock RTCPeerConnection
    const mockConnection = {
      createOffer: () => Promise.resolve({ type: 'offer', sdp: 'mock-sdp' }),
      createAnswer: () => Promise.resolve({ type: 'answer', sdp: 'mock-sdp' }),
      setLocalDescription: () => Promise.resolve(),
      setRemoteDescription: () => Promise.resolve(),
      addIceCandidate: () => Promise.resolve(),
      connectionState: 'connected',
      iceConnectionState: 'connected',
    } as any;

    // Simulate connection establishment
    setTimeout(() => {
      this.emit('peer-connected', { peer: { id: peerId, name: `Peer ${peerId}` } });
    }, 100);

    return mockConnection;
  }

  closeConnection(peerId: string): void {
    this.emit('peer-disconnected', { peer: { id: peerId } });
  }

  closeAllConnections(): void {
    this.emit('all-connections-closed');
  }

  sendData(peerId: string, data: any): void {
    // Simulate data sending
    console.log(`Sending data to ${peerId}:`, data);
  }

  createDataChannel(peerId: string, label: string): any {
    return {
      label,
      readyState: 'open',
      send: (data: any) => console.log(`Sending via data channel to ${peerId}:`, data),
    };
  }

  getConnectionState(peerId: string): any {
    return {
      connection: {
        connectionState: 'connected',
        setRemoteDescription: () => Promise.resolve(),
        addIceCandidate: () => Promise.resolve(),
      },
      dataChannel: {
        readyState: 'open',
      },
    };
  }
}

// Signaling Client stub
export class SignalingClient extends EventEmitter {
  private connected = false;

  async connect(url: string): Promise<void> {
    this.connected = true;
    this.emit('connected');
  }

  disconnect(): void {
    this.connected = false;
    this.emit('disconnected');
  }

  async joinRoom(roomId: string): Promise<string[]> {
    // Return mock existing peers
    const existingPeers = ['peer-1', 'peer-2'];
    this.emit('room-joined', { roomId, peers: existingPeers });
    return existingPeers;
  }

  leaveRoom(roomId: string): void {
    this.emit('room-left', { roomId });
  }

  sendOffer(peerId: string, offer: RTCSessionDescriptionInit): void {
    // Simulate sending offer
    setTimeout(() => {
      this.emit('answer-received', {
        senderId: peerId,
        answer: { type: 'answer', sdp: 'mock-answer-sdp' },
      });
    }, 50);
  }

  sendAnswer(peerId: string, answer: RTCSessionDescriptionInit): void {
    // Simulate sending answer
    console.log(`Sending answer to ${peerId}`);
  }

  sendIceCandidate(peerId: string, candidate: RTCIceCandidateInit): void {
    // Simulate sending ICE candidate
    console.log(`Sending ICE candidate to ${peerId}`);
  }

  getIsConnected(): boolean {
    return this.connected;
  }
}

// Message Handler stub
export class MessageHandler extends EventEmitter {
  sendMessage(peerId: string, content: string, replyTo?: string): Message {
    const message: Message = {
      id: `msg-${Date.now()}`,
      senderId: 'current-user',
      content,
      timestamp: new Date(),
      type: 'text',
      replyTo,
    };

    // Simulate message sending
    setTimeout(() => {
      this.emit('message-sent', { peerId, message });
    }, 10);

    return message;
  }

  handleReceivedMessage(peerId: string, data: any): void {
    const message: Message = {
      id: data.id || `msg-${Date.now()}`,
      senderId: peerId,
      content: data.content,
      timestamp: new Date(data.timestamp),
      type: data.type || 'text',
      replyTo: data.replyTo,
    };

    this.emit('message-received', { message });
  }
}

// File Transfer Manager stub
export class FileTransferManager extends EventEmitter {
  initiateTransfer(file: File, peerId: string, onProgress?: (progress: number) => void): FileTransfer {
    const transfer: FileTransfer = {
      id: `transfer-${Date.now()}`,
      fileName: file.name,
      fileSize: file.size,
      senderId: 'current-user',
      receiverId: peerId,
      status: 'pending',
      progress: 0,
      timestamp: new Date(),
    };

    // Simulate transfer progress
    setTimeout(() => {
      this.emit('transfer-initiated', { transfer });
      
      // Simulate progress updates
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        if (onProgress) onProgress(progress);
        this.emit('transfer-progress', { transferId: transfer.id, progress });
        
        if (progress >= 100) {
          clearInterval(interval);
          this.emit('transfer-completed', { transferId: transfer.id });
        }
      }, 100);
    }, 50);

    return transfer;
  }

  acceptTransfer(transferId: string): void {
    this.emit('transfer-accepted', { transferId });
  }

  rejectTransfer(transferId: string): void {
    this.emit('transfer-rejected', { transferId });
  }

  cancelTransfer(transferId: string): void {
    this.emit('transfer-cancelled', { transferId });
  }
}

// Default P2P configuration
export const defaultP2PConfig: P2PConfig = {
  signalingServer: 'ws://localhost:3001',
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
  ],
  connectionTimeout: 30000,
  heartbeatInterval: 5000,
  maxRetries: 3,
  chunkSize: 16384,
};
