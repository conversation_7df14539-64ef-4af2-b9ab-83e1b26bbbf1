import { vi } from 'vitest';
import type { P2PConfig, PeerInfo, Message, ConnectionState } from '@p2p/types';

// Mock EventBus
export const mockEventBus = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock P2PConnectionManager
export const mockP2PConnectionManager = {
  createConnection: vi.fn(),
  closeConnection: vi.fn(),
  closeAllConnections: vi.fn(),
  sendData: vi.fn(),
  createDataChannel: vi.fn(),
  getConnectionState: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock SignalingClient
export const mockSignalingClient = {
  connect: vi.fn(),
  disconnect: vi.fn(),
  joinRoom: vi.fn(),
  leaveRoom: vi.fn(),
  sendOffer: vi.fn(),
  sendAnswer: vi.fn(),
  sendIceCandidate: vi.fn(),
  getIsConnected: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock MessageHandler
export const mockMessageHandler = {
  sendMessage: vi.fn(),
  handleReceivedMessage: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock FileTransferManager
export const mockFileTransferManager = {
  initiateTransfer: vi.fn(),
  acceptTransfer: vi.fn(),
  rejectTransfer: vi.fn(),
  cancelTransfer: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock default config
export const mockDefaultP2PConfig: P2PConfig = {
  iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
  signalingUrl: 'http://localhost:3001',
  maxReconnectAttempts: 3,
  reconnectDelay: 1000,
  chunkSize: 16384,
  maxFileSize: 100 * 1024 * 1024,
};

// Mock implementations
vi.mock('@p2p/core', () => ({
  P2PConnectionManager: vi
    .fn()
    .mockImplementation(() => mockP2PConnectionManager),
  SignalingClient: vi.fn().mockImplementation(() => mockSignalingClient),
  MessageHandler: vi.fn().mockImplementation(() => mockMessageHandler),
  FileTransferManager: vi
    .fn()
    .mockImplementation(() => mockFileTransferManager),
  EventBus: vi.fn().mockImplementation(() => mockEventBus),
  defaultP2PConfig: mockDefaultP2PConfig,
}));

// Helper functions for tests
export const createMockPeerInfo = (
  overrides?: Partial<PeerInfo>
): PeerInfo => ({
  id: 'mock-peer-id',
  status: 'connected',
  name: 'Mock Peer',
  ...overrides,
});

export const createMockMessage = (overrides?: Partial<Message>): Message => ({
  id: 'mock-message-id',
  senderId: 'mock-sender-id',
  content: 'Mock message content',
  timestamp: new Date(),
  type: 'text',
  ...overrides,
});

export const createMockConnectionState = (
  overrides?: Partial<ConnectionState>
): ConnectionState => ({
  peer: createMockPeerInfo(),
  status: 'connected',
  connection: null,
  dataChannel: null,
  lastActivity: new Date(),
  ...overrides,
});

// Reset all mocks
export const resetP2PMocks = () => {
  Object.values(mockP2PConnectionManager).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });

  Object.values(mockSignalingClient).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });

  Object.values(mockMessageHandler).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });

  Object.values(mockFileTransferManager).forEach(mock => {
    if (typeof mock === 'function' && 'mockReset' in mock) {
      mock.mockReset();
    }
  });
};
