import {
  P2PConnectionManager,
  SignalingClient,
  MessageHandler,
  FileTransferManager,
  defaultP2PConfig,
} from './p2p-core-stubs';
import type { P2PConfig, PeerInfo, Message } from '../types/p2p';
import { useP2PStore } from '../stores/useP2PStore';

class P2PService {
  private connectionManager: P2PConnectionManager;
  private signalingClient: SignalingClient;
  private messageHandler: MessageHandler;
  private fileTransferManager: FileTransferManager;
  private config: P2PConfig;
  private currentUserId: string | null = null;

  constructor(config?: Partial<P2PConfig>) {
    this.config = { ...defaultP2PConfig, ...config };
    this.connectionManager = new P2PConnectionManager(this.config);
    this.signalingClient = new SignalingClient(this.config);
    this.messageHandler = new MessageHandler();
    this.fileTransferManager = new FileTransferManager(this.config.chunkSize);

    this.setupEventHandlers();
  }

  // Initialize the service
  async initialize(userId: string, userName: string): Promise<void> {
    this.currentUserId = userId;
    useP2PStore.getState().setCurrentUser(userId, userName);

    try {
      await this.signalingClient.connect();
      useP2PStore.getState().setConnected(true);
      console.log('P2P Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize P2P Service:', error);
      throw error;
    }
  }

  // Join a room
  async joinRoom(roomId: string): Promise<void> {
    try {
      useP2PStore.getState().setConnecting(true);
      const existingPeers = await this.signalingClient.joinRoom(roomId);
      useP2PStore.getState().setCurrentRoomId(roomId);

      // Create connections to existing peers
      for (const peerId of existingPeers) {
        await this.createPeerConnection(peerId);
      }

      useP2PStore.getState().setConnecting(false);
      console.log(
        `Joined room ${roomId} with ${existingPeers.length} existing peers`
      );
    } catch (error) {
      useP2PStore.getState().setConnecting(false);
      console.error('Failed to join room:', error);
      throw error;
    }
  }

  // Leave current room
  leaveRoom(): void {
    this.signalingClient.leaveRoom();
    this.connectionManager.closeAllConnections();
    useP2PStore.getState().setCurrentRoomId(null);
    useP2PStore.getState().reset();
  }

  // Send a message to a peer
  sendMessage(peerId: string, content: string, replyTo?: string): Message {
    const message = this.messageHandler.sendMessage(
      peerId,
      content,
      (targetPeerId, data) =>
        this.connectionManager.sendData(targetPeerId, data),
      replyTo
    );

    // Update the message with current user ID
    message.senderId = this.currentUserId || 'unknown';
    useP2PStore.getState().addMessage(message);

    return message;
  }

  // Send a message to all connected peers
  broadcastMessage(content: string): Message[] {
    const store = useP2PStore.getState();
    const connectedPeers = Array.from(store.peers.values()).filter(
      peer => store.connectionStates.get(peer.id)?.status === 'connected'
    );

    return connectedPeers.map(peer => this.sendMessage(peer.id, content));
  }

  // Initiate file transfer
  initiateFileTransfer(file: File, receiverId: string): void {
    const transfer = this.fileTransferManager.initiateTransfer(
      file,
      receiverId,
      (peerId, data) => this.connectionManager.sendData(peerId, data)
    );

    useP2PStore.getState().addFileTransfer(transfer);
  }

  // Accept file transfer
  acceptFileTransfer(transferId: string): void {
    this.fileTransferManager.acceptTransfer(transferId, (peerId, data) =>
      this.connectionManager.sendData(peerId, data)
    );
  }

  // Reject file transfer
  rejectFileTransfer(transferId: string): void {
    this.fileTransferManager.rejectTransfer(transferId, (peerId, data) =>
      this.connectionManager.sendData(peerId, data)
    );

    useP2PStore.getState().removeFileTransfer(transferId);
  }

  // Get connection status
  getConnectionStatus(): boolean {
    return this.signalingClient.getIsConnected();
  }

  // Disconnect
  disconnect(): void {
    this.signalingClient.disconnect();
    this.connectionManager.closeAllConnections();
    useP2PStore.getState().reset();
  }

  private async createPeerConnection(peerId: string): Promise<void> {
    const peerInfo: PeerInfo = {
      id: peerId,
      status: 'connecting',
    };

    try {
      const connection = await this.connectionManager.createConnection(
        peerId,
        peerInfo
      );
      useP2PStore.getState().addPeer(peerInfo);

      // Create data channel
      this.connectionManager.createDataChannel(peerId);

      // Create and send offer
      const offer = await connection.createOffer();
      await connection.setLocalDescription(offer);
      this.signalingClient.sendOffer(peerId, offer);
    } catch (error) {
      console.error(`Failed to create connection to peer ${peerId}:`, error);
    }
  }

  private setupEventHandlers(): void {
    // Connection manager events
    this.connectionManager.on('peer-connected', event => {
      const store = useP2PStore.getState();
      store.addPeer(event.peer);
      store.addNotification({
        id: `peer-connected-${event.peer.id}`,
        type: 'success',
        title: 'Peer Connected',
        message: `Connected to ${event.peer.name || event.peer.id}`,
        timestamp: new Date(),
        autoClose: true,
        duration: 3000,
      });
    });

    this.connectionManager.on('peer-disconnected', event => {
      const store = useP2PStore.getState();
      store.removePeer(event.peer.id);
      store.addNotification({
        id: `peer-disconnected-${event.peer.id}`,
        type: 'warning',
        title: 'Peer Disconnected',
        message: `${event.peer.name || event.peer.id} disconnected`,
        timestamp: new Date(),
        autoClose: true,
        duration: 3000,
      });
    });

    this.connectionManager.on('connection-state-changed', event => {
      useP2PStore
        .getState()
        .updateConnectionState(event.state.peer.id, event.state);
    });

    // Data received from connection manager - forward to message handler
    this.connectionManager.on('data-received', event => {
      this.messageHandler.handleReceivedMessage(event.peerId, event.data);
    });

    // Message handler events
    this.messageHandler.on('message-received', event => {
      useP2PStore.getState().addMessage(event.message);
    });

    // File transfer events
    this.fileTransferManager.on('file-transfer-request', event => {
      useP2PStore.getState().addFileTransfer(event.transfer);
      useP2PStore.getState().addNotification({
        id: `file-transfer-${event.transfer.id}`,
        type: 'info',
        title: 'File Transfer Request',
        message: `${event.transfer.fileName} from ${event.transfer.senderId}`,
        timestamp: new Date(),
        autoClose: false,
      });
    });

    this.fileTransferManager.on('file-transfer-progress', event => {
      useP2PStore.getState().updateFileTransfer(event.transferId, {
        progress: event.progress,
      });
    });

    // Signaling events (WebRTC negotiation)
    this.signalingClient.on('peer-joined' as any, async (event: any) => {
      const { peerId } = event;
      console.log(`Handling peer-joined event for peer ${peerId}`);
      // When a new peer joins, existing peers should create connections to them
      await this.createPeerConnection(peerId);
    });

    this.signalingClient.on('offer-received' as any, async (event: any) => {
      const { senderId, offer } = event;
      try {
        const peerInfo: PeerInfo = { id: senderId, status: 'connecting' };
        const connection = await this.connectionManager.createConnection(
          senderId,
          peerInfo
        );

        await connection.setRemoteDescription(offer);
        const answer = await connection.createAnswer();
        await connection.setLocalDescription(answer);

        this.signalingClient.sendAnswer(senderId, answer);
      } catch (error) {
        console.error('Failed to handle offer:', error);
      }
    });

    this.signalingClient.on('answer-received' as any, async (event: any) => {
      const { senderId, answer } = event;
      const state = this.connectionManager.getConnectionState(senderId);
      if (state?.connection) {
        try {
          await state.connection.setRemoteDescription(answer);
        } catch (error) {
          console.error('Failed to handle answer:', error);
        }
      }
    });

    this.signalingClient.on(
      'ice-candidate-received' as any,
      async (event: any) => {
        const { senderId, candidate } = event;
        const state = this.connectionManager.getConnectionState(senderId);
        if (state?.connection) {
          try {
            await state.connection.addIceCandidate(candidate);
          } catch (error) {
            console.error('Failed to handle ICE candidate:', error);
          }
        }
      }
    );
  }
}

// Export singleton instance
export const p2pService = new P2PService({
  signalingUrl: import.meta.env.VITE_SIGNALING_URL || 'http://localhost:3003',
});

export default P2PService;
